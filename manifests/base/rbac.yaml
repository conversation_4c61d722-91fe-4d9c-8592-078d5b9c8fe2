apiVersion: v1
kind: ServiceAccount
metadata:
  name: authservice

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: authn-delegator
rules:
  - apiGroups:
      - authentication.k8s.io
    resources:
      - tokenreviews
    verbs:
      - create

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: authn-delegators
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: authn-delegator
subjects:
  - kind: ServiceAccount
    name: authservice
