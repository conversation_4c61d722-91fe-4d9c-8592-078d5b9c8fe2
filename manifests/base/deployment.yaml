apiVersion: apps/v1
kind: Deployment
metadata:
  name: qingcloud-auth-authservice
  labels:
    app: authservice
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: qingcloud-auth
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "false"
      labels:
        app.kubernetes.io/name: qingcloud-auth
    spec:
      imagePullSecrets:
        - name: aicp-docker-images-pull
      containers:
        - name: qingcloud-auth
          image: docker.io/aicphub/auth-server:v2.2.0
          imagePullPolicy: Always
          ports:
            - name: http-api
              containerPort: 8080
              protocol: TCP
          envFrom:
            - configMapRef:
                name: qingcloud-ext-auth-server
          livenessProbe:
            periodSeconds: 60
            httpGet:
              path: /healthz
              port: http-api
              scheme: HTTP
