apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - secret.yaml
  - rbac.yaml
  - service.yaml
  - envoy-filter.yaml
  - deployment.yaml

configMapGenerator:
  - envs:
      - params.env
    name: qingcloud-ext-auth-server

namespace: istio-system

generatorOptions:
  disableNameSuffixHash: true

vars:
  - name: AUTHSERVICE_NAMESPACE
    objref:
      kind: Service
      name: authservice
      apiVersion: v1
    fieldref:
      fieldpath: metadata.namespace
  - name: AUTHSERVICE_SERVICE
    objref:
      kind: Service
      name: authservice
      apiVersion: v1
    fieldref:
      fieldpath: metadata.name
configurations:
  - params.yaml
