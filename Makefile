# Makefile for building and pushing multi-architecture Docker images

# Default values
REPO ?= hub.kubesphere.com.cn/aicp/auth-server
ARCHS ?= linux/amd64,linux/arm64

# Docker buildx builder name
BUILDER_NAME ?= multiarch-builder

# Determine the tag
GIT_TAG := $(shell git describe --tags --exact-match 2>/dev/null)
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_SHA := $(shell git rev-parse --short HEAD)
TAG ?= $(if $(GIT_TAG),$(GIT_TAG),$(GIT_BRANCH)-$(GIT_SHA))

# Default target
.PHONY: default
default: build

# Create a new builder instance if it doesn't exist
.PHONY: create-builder
create-builder:
	@if ! docker buildx inspect $(BUILDER_NAME) > /dev/null 2>&1; then \
		docker buildx create --name $(BUILDER_NAME) --use; \
		docker buildx inspect $(BUILDER_NAME) --bootstrap; \
	fi

# Build the Docker image for multiple architectures
.PHONY: build
build: create-builder
	docker buildx build --platform $(ARCHS) -t $(REPO):$(TAG) .

# Build and push the Docker image for multiple architectures
.PHONY: build-push
build-push: build
	docker buildx build --platform $(ARCHS) -t $(REPO):$(TAG) --push .

# Clean up the builder instance
.PHONY: clean
clean:
	docker buildx rm $(BUILDER_NAME)