import asyncio
from contextlib import asynccontextmanager
from functools import wraps

import aioredis
import pickle
from typing import Any, Callable, Optional, AsyncIterator, Tuple, Type

import app
from app.loggers import logger


def ignore_exception(
        exceptions: Optional[Tuple[Type[Exception], ...]] = None,
        default: Optional[Any] = None,
        log_error: bool = True,
):
    """
    忽略异常的装饰器，支持同步和异步函数。

    :param exceptions: 需要忽略的异常类型（默认为所有异常）。
    :param default: 发生异常时返回的默认值。
    :param log_error: 是否记录异常日志。
    :return: 装饰器函数。
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs) -> Optional[Any]:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if exceptions is None or isinstance(e, exceptions):
                    if log_error:
                        logger.error(f"Ignoring exception in {func.__name__}: {e}")
                    return default
                else:
                    raise  # 如果不是指定异常，重新抛出

        @wraps(func)
        def sync_wrapper(*args, **kwargs) -> Optional[Any]:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if exceptions is None or isinstance(e, exceptions):
                    if log_error:
                        logger.error(f"Ignoring exception in {func.__name__}: {e}")
                    return default
                else:
                    raise  # 如果不是指定异常，重新抛出

        # 根据函数是否是异步函数返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator


# 创建 Redis 连接池
def get_redis_client():
    """
    获取 Redis 连接池。
    :return:
    """
    return aioredis.from_url(app.settings.redis_url)


class AioRedisCache:
    """
    Redis 缓存类。
    """

    def __init__(self):
        """
        初始化 RedisCache 类。

        :param redis_url: Redis 连接 URL，默认为 "redis://localhost:6379"
        """
        pass

    @asynccontextmanager
    async def _get_connection(self) -> AsyncIterator[aioredis.Redis]:
        """
        从连接池中获取一个 Redis 连接。

        使用 `async with` 语法自动管理连接的获取和释放。
        """
        yield get_redis_client()

    @ignore_exception()
    async def set_cache(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存。

        :param key: 缓存键
        :param value: 缓存值
        :param ttl: 缓存过期时间（秒），默认为 None（永不过期）
        :return: 是否设置成功
        """
        async with self._get_connection() as conn:
            # 序列化值
            serialized_value = pickle.dumps(value)

            # 设置缓存
            if ttl is not None:
                return await conn.set(key, serialized_value, ex=ttl)
            else:
                return await conn.set(key, serialized_value)

    @ignore_exception()
    async def get_cache(self, key: str) -> Optional[Any]:
        """
        获取缓存。

        :param key: 缓存键
        :return: 缓存值，如果键不存在则返回 None
        """
        async with self._get_connection() as conn:
            # 获取缓存
            serialized_value = await conn.get(key)
            if serialized_value is not None:
                return pickle.loads(serialized_value)
            return None

    @ignore_exception()
    async def unset_cache(self, key: str) -> bool:
        """
        删除缓存。

        :param key: 缓存键
        :return: 是否删除成功
        """
        async with self._get_connection() as conn:
            # 删除缓存
            return await conn.delete(key) > 0


aio_redis_cache: AioRedisCache = AioRedisCache()


# 缓存装饰器
def cache(ttl: int = 3600):
    """
    缓存装饰器，将函数的返回值缓存到 Redis 中。

    仅用于普通函数.

    :param ttl: 缓存过期时间（秒），默认为 3600 秒
    """

    def decorator(func: Callable) -> Callable:
        """
        缓存装饰器的装饰函数。
        :param func:
        :return:
        """

        @wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            """
            缓存装饰器的包装函数。
            :param args:
            :param kwargs:
            :return:
            """
            # 生成缓存的唯一键
            cache_key = f"aicp:auth:{func.__name__}:{args}:{kwargs}"

            # 获取 Redis 连接池
            redis = get_redis_client()
            try:
                # 尝试从缓存中获取数据
                cached_data = await redis.get(cache_key)
                if cached_data is not None:
                    # 如果缓存存在，直接返回缓存数据
                    return pickle.loads(cached_data)
            except Exception as e:
                logger.error(f"Failed to get cache from Redis: {e}")

            # 如果缓存不存在，调用原始函数
            result = await func(*args, **kwargs)

            try:
                # 将结果缓存到 Redis 中
                await redis.set(cache_key, pickle.dumps(result), ex=ttl)
            except Exception as e:
                logger.error(f"Failed to set cache to Redis: {e}")

            # 返回结果
            return result

        return wrapper

    return decorator
