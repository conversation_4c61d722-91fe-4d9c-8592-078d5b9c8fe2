import re
import httpx
import json
import uuid

from fastapi import <PERSON><PERSON><PERSON>, Request
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import J<PERSON><PERSON>esponse, Response

from app import settings, request_id_context
from app.loggers import logger
from app.utils import AuthnException, check_inf_permission, check_session_key, check_signature, get_user_info, \
    is_exclude_url, \
    need_patch_user_info_url

application = FastAPI(docs_url=None, redoc_url=None, openapi_url=None)
INF_API_PATT = re.compile(settings.MAAS_INF_API_PATTERN)

application.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

application.add_exception_handler(
    AuthnException,
    lambda request, exc: JSONResponse({"ret_code": 403, "ret_message": str(exc)}, status_code=403)
)


@application.middleware("http")
async def add_request_id(request: Request, call_next):
    request_id = str(uuid.uuid4())[:8]
    request_id_context.set(request_id)
    response = await call_next(request)
    request_id_context.set(None)
    return response


@application.get("/healthz")
def healthz():
    """
    健康检查
    """
    return "ok"


@application.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE"])
async def authn(request: Request, response: Response):
    """
    验证 qingcloud 请求是否有效
    qingcloud 请求存在两种形式:
        1. 从console发起的请求, 比如jupyter页面过来的请求, 这种请求会带有 cookie: sk, 需要通过qingcloud account server 验证session是否有效
        2. 另一种是通过webserver发起的 api 调用,  这种需要验证 signature 是够有效.
    """
    logger.info(f"request.url.path: {request.url.path}")
    if is_exclude_url(request.url.path):
        logger.info(f"exclude url for {request.url.path}, skip!")
        return JSONResponse({"ret_code": 200, "ret_message": "exclude url, skip!"})

    headers = request.headers
    sk = request.cookies.get("sk")
    if INF_API_PATT.match(request.url.path):
        return await check_inf_permission(request)
    elif request.url.path in settings.CHECK_SK_URLS:
        user_id: str = await check_session_key(sk)
    elif request.url.path in settings.CHECK_API_URLS:
        user_id: str = await check_signature(request)
    elif headers.get("Channel") == "api" or "signature" in request.query_params.keys():
        # 通过 webserver 发起的 api 调用, 需要验证 signature
        user_id: str = await check_signature(request)
    else:
        user_id: str = await check_session_key(sk)

    response.headers.setdefault("aicp-userid", user_id)
    response.headers.setdefault("kubeflow-userid", user_id)
    if need_patch_user_info_url(request.url.path):
        user_info = await get_user_info(user_id)
        response.headers.setdefault("aicp-userinfo", json.dumps(user_info))
    logger.info(f"authorized : request.url.path: {request.url}, user_id: {user_id}")

    return "authorized"
