from typing import Any, Dict, List

from pydantic import BaseSettings
from starlette.responses import Response


class Settings(BaseSettings):
    # qingcloud
    qingcloud_access_key_id: str
    qingcloud_secret_access_key: str
    qingcloud_zone: str
    qingcloud_host: str
    qingcloud_port: str
    qingcloud_protocol: str

    CHECK_SK_URLS: List[str] = []
    CHECK_API_URLS: List[str] = []

    EXCLUDE_URL: List[str] = ["/docs", "/openapi.json"]
    MAAS_INF_API_PATTERN = '/.*/inference/(admin|usr-\w+)/.*'
    MAAS_INF_API_BASE_URL = 'http://model-manage-server.maas-system.svc/'
    MAAS_INF_API_AUTH_URL = '/maas/api/inf-svc/auth'

    REDIS_HOST: str
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""

    # Global服务地址
    GLOBAL_SERVER_URL: str = "https://hpc-ai.qingcloud.com"

    INNER_SERVERS_PREFIX: List[str] = ["/aicp", "/qai/aicp", "/epfs", "/qai/epfs", "/docker", "/qai/docker", "/maas",
                                       "/qai/maas"]

    @property
    def qingcloud(self) -> Dict[str, Any]:
        """QingCloud configuration"""
        return {
            "qy_access_key_id": self.qingcloud_access_key_id,
            "qy_secret_access_key": self.qingcloud_secret_access_key,
            "zone": self.qingcloud_zone,
            "host": self.qingcloud_host,
            "port": self.qingcloud_port,
            "protocol": self.qingcloud_protocol
        }

    @property
    def global_server(self) -> Dict[str, Any]:
        """Global server configuration"""
        # split GLOBAL_SERVER_URL to protocol , host and port, the default port is 443 if protocol is https else 80
        protocol, host_str = self.GLOBAL_SERVER_URL.split("://")
        host, port = host_str.split(":") if ":" in host_str else (host_str, "443" if protocol == "https" else "80")

        return {
            "qy_access_key_id": self.qingcloud_access_key_id,
            "qy_secret_access_key": self.qingcloud_secret_access_key,
            "zone": self.qingcloud_zone,
            "host": host,
            "port": port,
            "protocol": protocol
        }

    @property
    def redis_url(self) -> str:
        """Redis URL"""
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}"


AuthorizedResponse = Response(content="authorized", )
