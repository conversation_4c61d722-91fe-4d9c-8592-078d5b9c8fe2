"""
...
"""
import asyncio
import base64
import hashlib
import hmac
import os
import re
from collections import OrderedDict
from hashlib import sha256
from random import random
from typing import Dict, Tuple
from urllib import parse

import aiohttp
from qingcloud.iaas import APIConnection
from starlette.requests import Request
from starlette.responses import JSONResponse, Response

from app import settings
from app.cache import aio_redis_cache, cache, ignore_exception
from app.config import AuthorizedResponse
from app.loggers import logger


class AuthnException(Exception):
    """
    鉴权异常
    """
    pass


exclude_complicated_urls = [
    re.compile(x) for x in settings.EXCLUDE_URL
]


def is_exclude_url(url: str) -> bool:
    """
    判断 url 是否在排除列表中, 使用正则表达式进行验证
    """
    for pattern in exclude_complicated_urls:
        if pattern.match(url):
            return True
    return False


class AsyncApiConnection(APIConnection):
    """
    Public async connection to qingcloud service

    """

    def __init__(self, qy_access_key_id, qy_secret_access_key, zone, host="api.qingcloud.com", port=443,
                 protocol="https", pool=None, expires=None, retry_time=2, http_socket_timeout=60, debug=False,
                 credential_proxy_host="***************", credential_proxy_port=80):
        """
        @param qy_access_key_id - the access key id
        @param qy_secret_access_key - the secret access key
        @param zone - the zone id to access
        @param host - the host to make the connection to
        @param port - the port to use when connect to host
        @param protocol - the protocol to access to web server, "http" or "https"
        @param pool - the connection pool
        @param retry_time - the retry_time when message send fail
        """

        super().__init__(qy_access_key_id, qy_secret_access_key, zone, host, port, protocol, pool, expires, retry_time,
                         http_socket_timeout, debug, credential_proxy_host, credential_proxy_port)
        self._base_url = f"{protocol}://{host}:{port}"

    async def send_request(self, action, body, url="/iaas/", verb="GET"):
        """ Send request
        """
        body['action'] = action
        body['zone'] = self.zone
        if self.expires:
            body['expires'] = self.expires

        # Build the http request
        request = self.build_http_request(verb, url, body)
        request.authorize(self)

        retry_time = 0
        while retry_time < self.retry_time:
            # Use binary exponential backoff to synchronize client requests
            try:
                async with aiohttp.ClientSession(base_url=self._base_url) as session:
                    async with session.request(method=request.method, url=request.path, headers=request.header,
                                               data=request.body) as response:
                        logger.debug(f"Get Iaas request {body}, response: {await response.text()}")
                        return await response.json()
            except Exception as e:
                logger.error(f"send request failed, retry_time: {retry_time}, error: {e}")
                await asyncio.sleep(random() * (2 ** retry_time))
            retry_time += 1


qingcloud_api_client = AsyncApiConnection(**settings.qingcloud)


@cache(ttl=86400)
async def describe_access_key(access_key_id):
    """
    :param access_key_id:
    :return:
    """
    req = {
        'access_keys': [access_key_id]
    }
    action = "DescribeAccessKeys"
    rsp = await qingcloud_api_client.send_request(action, req)
    access_key_set = rsp.get("access_key_set", [])
    if not access_key_set:
        raise AuthnException("access_key_set is empty")

    return access_key_set[0]


def hex_encode_md5_hash(data):
    """

    :param data:
    :return:
    """
    if not data:
        data = "".encode("utf-8")
    else:
        data = data.encode("utf-8")
    md5 = hashlib.md5()
    md5.update(data)
    return md5.hexdigest()


def get_signature(method: str, url: str, ak: str, sk: str, params: dict):
    """
    :param url: /api/test/  must be ended /
    :param ak: access_key_id
    :param sk:  secure_key
    :param params: dict type
    :param method: method GET POST PUT DELETE
    :return:
    """
    url += "/" if not url.endswith("/") else ""
    params["access_key_id"] = ak
    sorted_param = OrderedDict()
    keys = sorted(params.keys())
    for key in keys:
        if isinstance(params[key], list):
            sorted_param[key] = sorted(params[key])
        else:
            sorted_param[key] = params[key]

    # sorted_param = {key: params.get(key) for key in keys}
    url_param = parse.urlencode(sorted_param, safe='/', quote_via=parse.quote)
    string_to_sign = method + "\n" + url + "\n" + url_param + "\n" + hex_encode_md5_hash("")

    h = hmac.new(sk.encode(encoding="utf-8"), digestmod=sha256)
    h.update(string_to_sign.encode(encoding="utf-8"))
    sign = base64.b64encode(h.digest()).strip()
    signature = parse.quote_plus(sign)
    url_param += "&signature=%s" % signature

    return url_param


async def check_signature(request: Request) -> str:
    """
    验证 qingcloud 请求是否有效
    :param request:
    :return:
    """
    method = request.method.upper()
    url = request.url.path
    url += "/" if not url.endswith("/") else ""

    access_key_id = request.query_params.get("access_key_id")
    req_signature = request.query_params.get("signature")

    if not access_key_id:
        raise AuthnException("access_key_id is empty")

    access_key_set = await describe_access_key(access_key_id)

    secret_access_key = access_key_set["secret_access_key"]

    query_keys = set()
    multi_keys = set()
    # find duplicate key in request.query_params.multi_items()
    for key, value in request.query_params.multi_items():
        if key in query_keys:
            multi_keys.add(key)
        query_keys.add(key)

    sorted_param = OrderedDict()
    keys = sorted(request.query_params.keys())
    for key in keys:
        if key in multi_keys:
            sorted_param[key] = sorted(request.query_params.getlist(key))
        else:
            sorted_param[key] = request.query_params[key]

    sorted_param.pop("signature", "")
    url_param = parse.urlencode(sorted_param, safe='/', quote_via=parse.quote, doseq=True)
    string_to_sign = method + "\n" + url + "\n" + url_param + "\n" + hex_encode_md5_hash("")

    h = hmac.new(secret_access_key.encode(encoding="utf-8"), digestmod=sha256)
    h.update(string_to_sign.encode(encoding="utf-8"))
    sign = base64.b64encode(h.digest()).strip()
    signature = parse.quote_plus(sign)
    if sign.decode() != req_signature and signature != req_signature:
        logger.warning(f"not match, sign: {sign}, signature: {signature}, req_signature: {req_signature}")
        raise AuthnException("signature is not match")

    return access_key_set["owner"]


@cache(ttl=600)
async def check_session_key(session_key: str) -> str:
    """
    验证 session key 是否有效
    :param session_key:
    :return:
    """
    if not session_key:
        raise AuthnException("session key is empty")

    req = {
        'sk': session_key
    }
    action = "CheckSession"
    rsp = await qingcloud_api_client.send_request(action, req)
    if rsp.get("ret_code") != 0:
        raise AuthnException("check session key failed")

    return rsp["owner"]


DEFAULT_GROUP_PERMISSION = {
    "permissions": [
        {
            "module": "NB",
            "module_name": "容器实例",
            "permission": "OWN"
        },
        {
            "module": "TN",
            "module_name": "训练任务",
            "permission": "OWN"
        },
        {
            "module": "INF",
            "module_name": "推理服务",
            "permission": "OWN"
        },
        {
            "module": "EPFS",
            "module_name": "存储与数据",
            "permission": "OWN"
        },
        {
            "module": "RG",
            "module_name": "专属资源",
            "permission": "OWN"
        }
    ]
}

USER_INFO_KEEP_KEYS = {
    # ak & sk
    'access_key_id', 'secret_access_key',
    # base user info
    'user_name', 'root_user_id', 'user_id', 'phone', 'email', 'gravatar_email', 'user_type', 'privilege', 'regions',
    'role', 'zones', 'iam_domain', 'personal_name', 'status', 'rating', 'lang', 'console_id',
    # group permission
    'group_info',
}

GROUP_INFO_KEEP_KEYS = {"permissions", "sub_acc_consume", "version"}


class GlobalPermissionClient(object):
    """
    get group auth info from global server
    """

    def __init__(self, user_id: str, access_key_id: str, secret_access_key: str) -> None:
        self._cache_key = f"aicp:auth:global_permission:{user_id}"
        self._user_id = user_id
        self._access_key_id = access_key_id
        self._secret_access_key = secret_access_key

    async def get_group_auth_info(self) -> dict:
        """
        1. get cached info
        2. get group info
        3. if cached version is None or not equal to group version, update cache, and return group info
        4. elif cached version is equal to group version, return cached info
        """
        cached_info = await aio_redis_cache.get_cache(self._cache_key)
        cached_version = 0
        if cached_info is not None:
            cached_version = cached_info.get("version", 0)

        try:
            url = "/global/team/api/user/auth"
            params = get_signature("GET", url, self._access_key_id, self._secret_access_key,
                                   {"version": cached_version})
            url += "?" + params
            async with aiohttp.ClientSession(base_url=settings.GLOBAL_SERVER_URL) as session:
                async with session.get(url) as response:
                    logger.debug(f"Get group info for user_id: {self._user_id} response: {await response.text()}")
                    group_info_resp = await response.json()
                    if group_info_resp.get("ret_code") != 0:
                        raise AuthnException("get group info failed")
                    group_info = group_info_resp.get("data")
        except Exception as e:
            logger.error(f"get group info failed, error: {e}")
            if cached_info is None:
                # if you get group info failed and cached info is None, return default group permission
                return DEFAULT_GROUP_PERMISSION
            return cached_info

        if cached_version != 0 and group_info is None:
            return cached_info

        group_info = {key: group_info.get(key) for key in GROUP_INFO_KEEP_KEYS}
        # the group info is updated, update cache
        await aio_redis_cache.set_cache(self._cache_key, group_info, ttl=3600)
        return group_info


@cache()
async def async_check_session_key(session_key: str) -> str:
    """
    验证 session key 是否有效
    :param session_key:
    :return:
    """
    if not session_key:
        raise AuthnException("session key is empty")

    req = {
        'sk': session_key
    }
    action = "CheckSession"
    rsp = await qingcloud_api_client.send_request(action, req)

    if rsp.get("ret_code") != 0:
        raise AuthnException("check session key failed")

    return rsp["owner"]


async def get_user_info(user_id) -> Dict:
    """
    get user info

    if describe user failed, raise AuthnException
    if describe access key failed, raise AuthnException
    if you get group auth info failed, patch default group permission

    :param user_id: str
    :return: a dict contains user info
    """
    user_info: Dict = await describe_user(user_id)
    access_key_info = await describe_access_key_by_user_id(user_id)
    user_info.update(access_key_info)
    client = GlobalPermissionClient(user_id, access_key_info["access_key_id"], access_key_info["secret_access_key"])
    user_info["group_info"] = await client.get_group_auth_info()
    return user_info


@cache(ttl=86400)
async def describe_user(user_id):
    """
    get user info
    :param user_id:
    :return:
    """
    req = {
        'users': [user_id]
    }
    action = "DescribeUsers"
    rsp = await qingcloud_api_client.send_request(action, req)
    if rsp.get("ret_code") != 0:
        raise AuthnException("describe user failed")
    if not rsp.get("user_set"):
        raise AuthnException("describe user failed")
    user_info = rsp["user_set"][0]
    list(map(user_info.pop, set(user_info.keys()) - USER_INFO_KEEP_KEYS))
    return rsp["user_set"][0]


@cache(ttl=86400)
async def describe_access_key_by_user_id(user_id) -> Dict:
    """
    get access key info
    :param user_id:
    :return:
    """
    req = {
        "owner": [user_id],
        "controller": "pitrix"
    }
    action = "DescribeAccessKeys"
    rsp = await qingcloud_api_client.send_request(action, req)
    if rsp.get("ret_code") != 0 or not rsp.get("access_key_set"):
        raise AuthnException("describe access key failed")

    return dict(
        access_key_id=rsp["access_key_set"][0]["access_key_id"],
        secret_access_key=rsp["access_key_set"][0]["secret_access_key"]
    )


@ignore_exception(default=(True, ""))
async def check_inf_api_key_permission(path, api_key) -> Tuple[bool, str]:
    """
    check inf api key permission
    :param path:
    :param api_key:
    :return: Default is True, ""
    """
    async with aiohttp.ClientSession(base_url=settings.MAAS_INF_API_BASE_URL) as session:
        async with session.get(settings.MAAS_INF_API_AUTH_URL, params={'path': path, 'api_key': api_key},
                               timeout=1) as response:
            ret = await response.json()
            if ret['ret_code'] != 0:
                return False, ret['message']
    return True, ""


@ignore_exception(default=False)
async def check_inf_sk_permission(user_id, session_key: str) -> bool:
    """
    if session key owner is equal to user_id, return True
    else return False include raise exception
    """
    if user_id == (await check_session_key(session_key)).lower():
        return True
    return False


async def check_inf_permission(request: Request) -> Response:
    """
    return 403 when sk/apiKey check failed only

    if iaas/maas connection failed, return default AuthorizedResponse=authorized
    """
    if sk := request.cookies.get("sk"):
        inf_user_id = request.url.path.split('/')[3]
        if await check_inf_sk_permission(inf_user_id, sk):
            return AuthorizedResponse

    if request.method in ['GET', 'POST']:
        api_key = request.headers.get('Authorization') or request.query_params.get('Authorization') or ''
        checked, message = await check_inf_api_key_permission(request.url.path, api_key)
        if not checked:
            return JSONResponse({'error': {'message': message, 'type': ''}}, status_code=403)

    return AuthorizedResponse


def need_patch_user_info_url(url: str) -> bool:
    """
    check if you need patch user info
    :param url:
    :return:
    """
    for prefix in settings.INNER_SERVERS_PREFIX:
        if url.startswith(prefix):
            return True
    return False


class KubesphereAPI:
    """
    KubeSphere API client for license and cluster status operations
    """

    def __init__(self, host="ks-console.kubesphere-system", port=80, protocol="http",
                 token_path="/var/run/secrets/kubesphere.io/serviceaccount/token"):
        """
        Initialize KubeSphere API client

        :param host: KubeSphere console host
        :param port: KubeSphere console port
        :param protocol: Protocol to use (http/https)
        :param token_path: Path to service account token file
        """
        self.host = host
        self.port = port
        self.protocol = protocol
        self.token_path = token_path
        self._base_url = f"{protocol}://{host}:{port}"
        self._token = None

    def _load_token(self):
        """Load service account token from file"""
        try:
            if os.path.exists(self.token_path):
                with open(self.token_path, 'r') as f:
                    self._token = f.read().strip()
            else:
                logger.warning(f"Token file not found at {self.token_path}")
                self._token = None
        except Exception as e:
            logger.error(f"Failed to load token from {self.token_path}: {e}")
            self._token = None

    def _get_headers(self):
        """Get HTTP headers with authorization"""
        if self._token is None:
            self._load_token()

        headers = {
            'Content-Type': 'application/json',
        }

        if self._token:
            headers['Authorization'] = f'Bearer {self._token}'

        return headers

    async def _make_request(self, method, path):
        """
        Make HTTP request to KubeSphere API

        :param method: HTTP method
        :param path: API path
        :return: Response JSON data
        """
        url = f"{self._base_url}{path}"
        headers = self._get_headers()

        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(method, url, headers=headers) as response:
                    logger.debug(f"KubeSphere API request {method} {url}, response status: {response.status}")
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.error(f"KubeSphere API request failed with status {response.status}: {await response.text()}")
                        return None
        except Exception as e:
            logger.error(f"KubeSphere API request failed: {e}")
            return None

    async def get_cluster_status(self):
        """
        Get cluster status from KubeSphere license API

        :return: hostClusterStatus dict or None if failed
        """
        path = "/kapis/license.kubesphere.io/v1alpha1/licenses/ks-core"
        response_data = await self._make_request("GET", path)

        if response_data:
            return response_data.get("hostClusterStatus")
        return None

    async def get_license_profile(self):
        """
        Get license profile from KubeSphere license API

        :return: profile dict or None if failed
        """
        path = "/kapis/license.kubesphere.io/v1alpha1/licenses/ks-core"
        response_data = await self._make_request("GET", path)

        if response_data:
            return response_data.get("profile")
        return None
