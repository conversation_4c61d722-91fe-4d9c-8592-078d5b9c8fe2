import logging

from app import request_id_context


class TraceIDFilter(logging.Filter):
    def filter(self, record):
        record.traceid = request_id_context.get()
        return True


def get_logger():
    """
    获取日志记录器
    :return:
    """
    logger_filter = TraceIDFilter("aicp")
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s(%(traceid)s)')
    # 创建一个StreamHandler，将日志记录到控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 获取或创建一个日志记录器
    _logger = logging.getLogger("auth")
    # logger.addHandler(file_handler)
    _logger.setLevel(logging.DEBUG)
    _logger.addHandler(console_handler)
    _logger.addFilter(logger_filter)
    return _logger


logger = get_logger()
