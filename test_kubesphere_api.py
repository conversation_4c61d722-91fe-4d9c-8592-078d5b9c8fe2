#!/usr/bin/env python3
"""
Test script for KubesphereAPI client
"""
import os
import tempfile
import unittest

# Set required environment variables before importing app modules
os.environ.setdefault('QINGCLOUD_ACCESS_KEY_ID', 'test_access_key_id')
os.environ.setdefault('QINGCLOUD_SECRET_ACCESS_KEY', 'test_secret_access_key')
os.environ.setdefault('QINGCLOUD_ZONE', 'test_zone')
os.environ.setdefault('QINGCLOUD_HOST', 'api.qingcloud.com')
os.environ.setdefault('QINGCLOUD_PORT', '443')
os.environ.setdefault('QINGCLOUD_PROTOCOL', 'https')
os.environ.setdefault('REDIS_HOST', 'localhost')

# Add the app directory to the path so we can import from it
import sys
sys.path.insert(0, 'app')

from utils import KubesphereAPI


class TestKubesphereAPI(unittest.IsolatedAsyncioTestCase):
    """Test case for KubesphereAPI client"""

    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary token file for testing
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False)
        self.temp_file.write('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************.YYDy7BYKCdXilE-rEuJMi4ffW6aZTS4kD4f4tByq9h0')
        self.temp_file.close()

        # Initialize the API client
        self.api = KubesphereAPI(
            host="jinan1.boss.aicp.qingcloud.link",
            port=80,
            protocol="http",
            token_path=self.temp_file.name
        )

    def tearDown(self):
        """Clean up test fixtures"""
        # Clean up the temporary token file
        os.unlink(self.temp_file.name)

    def test_initialization(self):
        """Test KubesphereAPI initialization"""
        self.assertEqual(self.api.host, "jinan1.boss.aicp.qingcloud.link")
        self.assertEqual(self.api.port, 80)
        self.assertEqual(self.api.protocol, "http")
        self.assertEqual(self.api._base_url, "http://jinan1.boss.aicp.qingcloud.link:80")
        self.assertEqual(self.api.token_path, self.temp_file.name)

    def test_token_loading(self):
        """Test token loading from file"""
        self.api._load_token()
        expected_token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************.YYDy7BYKCdXilE-rEuJMi4ffW6aZTS4kD4f4tByq9h0'
        self.assertEqual(self.api._token, expected_token)

    def test_headers_generation(self):
        """Test HTTP headers generation"""
        headers = self.api._get_headers()
        self.assertIn('Content-Type', headers)
        self.assertEqual(headers['Content-Type'], 'application/json')
        self.assertIn('Authorization', headers)
        self.assertTrue(headers['Authorization'].startswith('Bearer '))

    def test_api_methods_exist(self):
        """Test that required API methods exist"""
        self.assertTrue(hasattr(self.api, 'get_cluster_status'))
        self.assertTrue(hasattr(self.api, 'get_license_profile'))
        self.assertTrue(callable(getattr(self.api, 'get_cluster_status')))
        self.assertTrue(callable(getattr(self.api, 'get_license_profile')))

    def test_make_request_method(self):
        """Test the _make_request method exists and is callable"""
        self.assertTrue(hasattr(self.api, '_make_request'))
        self.assertTrue(callable(getattr(self.api, '_make_request')))

        # Note: We don't actually make the request since it would require a running KubeSphere instance
        # This test just verifies the method exists and can be called

    def test_token_file_not_found(self):
        """Test behavior when token file doesn't exist"""
        api_no_token = KubesphereAPI(token_path="/nonexistent/path/token")
        api_no_token._load_token()
        self.assertIsNone(api_no_token._token)


if __name__ == "__main__":
    unittest.main()
