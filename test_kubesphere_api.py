#!/usr/bin/env python3
"""
Test script for KubesphereAPI client
"""
import asyncio
import json
import os
import tempfile
from unittest.mock import patch, mock_open

# Add the app directory to the path so we can import from it
import sys
sys.path.insert(0, 'app')

from utils import KubesphereAPI


async def test_kubesphere_api():
    """Test KubesphereAPI functionality"""
    
    # Create a temporary token file for testing
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************.YYDy7BYKCdXilE-rEuJMi4ffW6aZTS4kD4f4tByq9h0')
        token_path = f.name
    
    try:
        # Initialize the API client
        api = KubesphereAPI(
            host="jinan1.boss.aicp.qingcloud.link",
            port=80,
            protocol="http",
            token_path=token_path
        )
        
        print(f"Initialized KubesphereAPI with base URL: {api._base_url}")
        print(f"Token path: {api.token_path}")
        
        # Test token loading
        api._load_token()
        print(f"Loaded token: {api._token}")
        
        # Test headers generation
        headers = api._get_headers()
        print(f"Generated headers: {json.dumps(headers, indent=2)}")
        
        # Note: The actual API calls would require a running KubeSphere instance
        # For testing purposes, we'll just verify the methods exist and can be called
        print("\nAPI methods available:")
        print(f"- get_cluster_status: {hasattr(api, 'get_cluster_status')}")
        print(f"- get_license_profile: {hasattr(api, 'get_license_profile')}")
        
        print("\nKubesphereAPI implementation completed successfully!")
        
    finally:
        # Clean up the temporary token file
        os.unlink(token_path)


if __name__ == "__main__":
    asyncio.run(test_kubesphere_api())
