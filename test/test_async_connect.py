import unittest

from app import settings
from app.utils import AsyncApiConnection


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.conn = AsyncApiConnection(
            settings.qingcloud.get('qingcloud_access_key_id'),
            settings.qingcloud.get('qingcloud_secret_access_key'),
            settings.qingcloud.get('qingcloud_default_zone'),
            host=settings.qingcloud.get('qingcloud_host'),
            port=settings.qingcloud.get('qingcloud_port'),
            protocol=settings.qingcloud.get('qingcloud_protocol')
        )

    async def test_send_req(self):
        req = {
            'access_keys': ["aaaaaaaaaaa"]
        }
        action = "DescribeAccessKeys"
        await self.conn.send_request(action, req)
